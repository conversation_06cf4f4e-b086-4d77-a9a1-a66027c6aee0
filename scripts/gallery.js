// =============================================================================
// GALLERY JAVASCRIPT - Tabbed Work Gallery
// =============================================================================

(function() {
  'use strict';

  // Gallery state
  let currentTab = 'logos';
  let galleryData = {
    logos: [],
    video: [],
    social: [],
    photo: []
  };

  // Initialize gallery
  function initGallery() {
    setupTabs();
    loadGalleryContent();
    setupImageHovers();
  }

  // Setup tab functionality
  function setupTabs() {
    const tabs = document.querySelectorAll('.work__tab');
    const panels = document.querySelectorAll('.work__panel');

    tabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        const tabId = tab.getAttribute('aria-controls');
        const category = tabId.replace('-panel', '');
        
        switchTab(category, tab, panels);
      });

      // Keyboard navigation
      tab.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          tab.click();
        }
      });
    });
  }

  // Switch active tab
  function switchTab(category, activeTab, panels) {
    // Update tab states
    document.querySelectorAll('.work__tab').forEach(tab => {
      tab.classList.remove('work__tab--active');
      tab.setAttribute('aria-selected', 'false');
    });

    activeTab.classList.add('work__tab--active');
    activeTab.setAttribute('aria-selected', 'true');

    // Update panel states
    panels.forEach(panel => {
      panel.classList.remove('work__panel--active');
    });

    const targetPanel = document.getElementById(`${category}-panel`);
    if (targetPanel) {
      targetPanel.classList.add('work__panel--active');
    }

    currentTab = category;
    
    // Trigger content load if needed
    loadTabContent(category);
  }

  // Load gallery content (placeholder for CMS integration)
  function loadGalleryContent() {
    // This would typically fetch from your CMS (Airtable)
    // For now, we'll use placeholder data
    
    galleryData = {
      logos: [
        { src: 'assets/images/placeholder-logo-1.jpg', alt: 'Logo Design 1', title: 'Brand Identity for TechStart' },
        { src: 'assets/images/placeholder-logo-2.jpg', alt: 'Logo Design 2', title: 'Restaurant Logo Design' },
        { src: 'assets/images/placeholder-logo-3.jpg', alt: 'Logo Design 3', title: 'Wellness Brand Logo' }
      ],
      video: [
        { src: 'assets/images/placeholder-video-1.jpg', alt: 'TikTok Video 1', title: 'Brand Awareness Campaign', type: 'video' },
        { src: 'assets/images/placeholder-video-2.jpg', alt: 'Instagram Reel 1', title: 'Product Showcase', type: 'video' }
      ],
      social: [
        { src: 'assets/images/placeholder-social-1.jpg', alt: 'Social Media Design 1', title: 'Instagram Post Series' },
        { src: 'assets/images/placeholder-social-2.jpg', alt: 'Social Media Design 2', title: 'Facebook Ad Campaign' }
      ],
      photo: [
        { src: 'assets/images/placeholder-photo-1.jpg', alt: 'Photography 1', title: 'Product Photography' },
        { src: 'assets/images/placeholder-photo-2.jpg', alt: 'Photography 2', title: 'Brand Lifestyle Shoot' }
      ]
    };

    // Load initial tab content
    loadTabContent(currentTab);
  }

  // Load content for specific tab
  function loadTabContent(category) {
    const panel = document.getElementById(`${category}-panel`);
    const gallery = panel?.querySelector('.work__gallery');
    
    if (!gallery || !galleryData[category]) return;

    // Clear existing content
    gallery.innerHTML = '';

    // Add items
    galleryData[category].forEach((item, index) => {
      const galleryItem = createGalleryItem(item, category, index);
      gallery.appendChild(galleryItem);
    });

    // Setup lazy loading for new images
    setupLazyLoadingForGallery(gallery);
  }

  // Create gallery item element
  function createGalleryItem(item, category, index) {
    const itemElement = document.createElement('div');
    itemElement.className = `work__item ${item.type === 'video' ? 'work__item--video' : ''}`;
    itemElement.setAttribute('data-category', category);
    itemElement.setAttribute('data-index', index);

    if (item.type === 'video') {
      itemElement.innerHTML = `
        <div class="phone-mockup">
          <img src="${item.src}" alt="${item.alt}" loading="lazy" class="gallery-image">
          <div class="play-button" aria-label="Play video" role="button" tabindex="0"></div>
        </div>
        <div class="work__item-overlay">
          <h4 class="work__item-title">${item.title}</h4>
        </div>
      `;
    } else {
      itemElement.innerHTML = `
        <img src="${item.src}" alt="${item.alt}" loading="lazy" class="gallery-image">
        <div class="work__item-overlay">
          <h4 class="work__item-title">${item.title}</h4>
        </div>
      `;
    }

    // Add click handler for lightbox or modal
    itemElement.addEventListener('click', () => {
      openItemModal(item, category, index);
    });

    return itemElement;
  }

  // Setup image hover effects
  function setupImageHovers() {
    document.addEventListener('mouseover', (e) => {
      if (e.target.classList.contains('gallery-image')) {
        e.target.style.transform = 'scale(1.05)';
      }
    });

    document.addEventListener('mouseout', (e) => {
      if (e.target.classList.contains('gallery-image')) {
        e.target.style.transform = 'scale(1)';
      }
    });
  }

  // Setup lazy loading for gallery images
  function setupLazyLoadingForGallery(gallery) {
    const images = gallery.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.classList.add('loaded');
            observer.unobserve(img);
          }
        });
      });

      images.forEach(img => imageObserver.observe(img));
    }
  }

  // Open item in modal/lightbox (placeholder)
  function openItemModal(item, category, index) {
    // This would open a modal or lightbox
    // For now, we'll just log the item
    console.log('Opening item:', item, 'from category:', category, 'at index:', index);
    
    // You could implement a lightbox here
    // or redirect to a detailed view
  }

  // CMS Integration Functions (for future use)
  // =============================================================================

  // Fetch content from Airtable (placeholder)
  async function fetchFromCMS(category) {
    try {
      // This would be your actual Airtable API call
      const response = await fetch(`/api/gallery/${category}`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching CMS data:', error);
      return [];
    }
  }

  // Update gallery from CMS
  async function updateGalleryFromCMS() {
    const categories = ['logos', 'video', 'social', 'photo'];
    
    for (const category of categories) {
      try {
        const data = await fetchFromCMS(category);
        galleryData[category] = data;
        
        // Refresh current tab if it matches
        if (category === currentTab) {
          loadTabContent(category);
        }
      } catch (error) {
        console.error(`Error updating ${category} gallery:`, error);
      }
    }
  }

  // Auto-refresh gallery content (for live updates)
  function setupAutoRefresh() {
    // Refresh every 5 minutes
    setInterval(updateGalleryFromCMS, 5 * 60 * 1000);
  }

  // Public API
  window.Gallery = {
    init: initGallery,
    switchTab: switchTab,
    refresh: updateGalleryFromCMS,
    getCurrentTab: () => currentTab,
    getGalleryData: () => galleryData
  };

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initGallery);
  } else {
    initGallery();
  }

})();
