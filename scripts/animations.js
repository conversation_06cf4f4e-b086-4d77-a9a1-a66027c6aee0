// =============================================================================
// ANIMATIONS JAVASCRIPT - Micro-animations and Effects
// =============================================================================

(function() {
  'use strict';

  // Animation state
  let animationsEnabled = true;
  let reducedMotion = false;

  // Initialize animations
  function initAnimations() {
    checkMotionPreferences();
    setupScrollAnimations();
    setupHoverAnimations();
    setupLoadingAnimations();
    setupParallaxEffects();
  }

  // Check user's motion preferences
  function checkMotionPreferences() {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    reducedMotion = mediaQuery.matches;
    
    mediaQuery.addEventListener('change', (e) => {
      reducedMotion = e.matches;
      updateAnimationState();
    });

    updateAnimationState();
  }

  // Update animation state based on preferences
  function updateAnimationState() {
    animationsEnabled = !reducedMotion;
    document.body.classList.toggle('reduced-motion', reducedMotion);
    
    if (reducedMotion) {
      // Disable complex animations
      document.querySelectorAll('.hero__bg-element').forEach(el => {
        el.style.animation = 'none';
      });
    }
  }

  // Setup scroll-triggered animations
  function setupScrollAnimations() {
    if (!animationsEnabled) return;

    const animatedElements = document.querySelectorAll([
      '.service-card',
      '.work__item',
      '.testimonial',
      '.about__content',
      '.about__image'
    ].join(','));

    if ('IntersectionObserver' in window) {
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
          if (entry.isIntersecting) {
            // Stagger animations
            setTimeout(() => {
              entry.target.classList.add('animate-in');
            }, index * 100);
            
            observer.unobserve(entry.target);
          }
        });
      }, observerOptions);

      animatedElements.forEach(element => {
        element.classList.add('animate-ready');
        observer.observe(element);
      });
    } else {
      // Fallback: show all elements immediately
      animatedElements.forEach(element => {
        element.classList.add('animate-in');
      });
    }
  }

  // Setup hover animations
  function setupHoverAnimations() {
    if (!animationsEnabled) return;

    // Service card hover effects
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        if (!reducedMotion) {
          card.style.transform = 'translateY(-8px) scale(1.02)';
        }
      });

      card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0) scale(1)';
      });
    });

    // Work item hover effects
    const workItems = document.querySelectorAll('.work__item');
    workItems.forEach(item => {
      item.addEventListener('mouseenter', () => {
        if (!reducedMotion) {
          const img = item.querySelector('img');
          if (img) {
            img.style.transform = 'scale(1.1)';
          }
        }
      });

      item.addEventListener('mouseleave', () => {
        const img = item.querySelector('img');
        if (img) {
          img.style.transform = 'scale(1)';
        }
      });
    });

    // Button hover effects
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
      button.addEventListener('mouseenter', () => {
        if (!reducedMotion && button.classList.contains('btn--primary')) {
          button.style.transform = 'translateY(-2px)';
          button.style.boxShadow = 'var(--shadow-xl)';
        }
      });

      button.addEventListener('mouseleave', () => {
        button.style.transform = 'translateY(0)';
        button.style.boxShadow = '';
      });
    });

    // Social link hover effects
    const socialLinks = document.querySelectorAll('.social-link');
    socialLinks.forEach(link => {
      link.addEventListener('mouseenter', () => {
        if (!reducedMotion) {
          link.style.transform = 'translateY(-2px) scale(1.1)';
        }
      });

      link.addEventListener('mouseleave', () => {
        link.style.transform = 'translateY(0) scale(1)';
      });
    });
  }

  // Setup loading animations
  function setupLoadingAnimations() {
    // Animate elements as they load
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (img.complete) {
        img.classList.add('loaded');
      } else {
        img.addEventListener('load', () => {
          img.classList.add('loaded');
        });
      }
    });

    // Page load animation
    window.addEventListener('load', () => {
      document.body.classList.add('page-loaded');
      
      if (animationsEnabled) {
        // Animate hero content
        const heroContent = document.querySelector('.hero__content');
        if (heroContent) {
          heroContent.style.opacity = '0';
          heroContent.style.transform = 'translateY(30px)';
          
          setTimeout(() => {
            heroContent.style.transition = 'all 0.8s ease-out';
            heroContent.style.opacity = '1';
            heroContent.style.transform = 'translateY(0)';
          }, 300);
        }
      }
    });
  }

  // Setup parallax effects
  function setupParallaxEffects() {
    if (!animationsEnabled || reducedMotion) return;

    let ticking = false;

    function updateParallax() {
      const scrolled = window.pageYOffset;
      const parallaxElements = document.querySelectorAll('[data-parallax]');

      parallaxElements.forEach(element => {
        const speed = element.dataset.parallax || 0.5;
        const yPos = -(scrolled * speed);
        element.style.transform = `translateY(${yPos}px)`;
      });

      ticking = false;
    }

    function requestTick() {
      if (!ticking) {
        requestAnimationFrame(updateParallax);
        ticking = true;
      }
    }

    window.addEventListener('scroll', requestTick);
  }

  // Utility function to create staggered animations
  function staggerAnimation(elements, delay = 100) {
    if (!animationsEnabled) return;

    elements.forEach((element, index) => {
      setTimeout(() => {
        element.classList.add('animate-in');
      }, index * delay);
    });
  }

  // Utility function to animate number counting
  function animateNumber(element, start, end, duration = 2000) {
    if (!animationsEnabled) {
      element.textContent = end;
      return;
    }

    const startTime = performance.now();
    const startValue = start;
    const endValue = end;

    function updateNumber(currentTime) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3);
      
      const currentValue = Math.round(startValue + (endValue - startValue) * easeOut);
      element.textContent = currentValue;

      if (progress < 1) {
        requestAnimationFrame(updateNumber);
      }
    }

    requestAnimationFrame(updateNumber);
  }

  // Utility function to create typing effect
  function typeWriter(element, text, speed = 50) {
    if (!animationsEnabled) {
      element.textContent = text;
      return;
    }

    let i = 0;
    element.textContent = '';

    function type() {
      if (i < text.length) {
        element.textContent += text.charAt(i);
        i++;
        setTimeout(type, speed);
      }
    }

    type();
  }

  // Utility function to create fade-in effect
  function fadeIn(element, duration = 500) {
    if (!animationsEnabled) {
      element.style.opacity = '1';
      return;
    }

    element.style.opacity = '0';
    element.style.transition = `opacity ${duration}ms ease-in-out`;
    
    setTimeout(() => {
      element.style.opacity = '1';
    }, 10);
  }

  // Utility function to create slide-in effect
  function slideIn(element, direction = 'up', duration = 500) {
    if (!animationsEnabled) {
      element.style.transform = 'none';
      element.style.opacity = '1';
      return;
    }

    const transforms = {
      up: 'translateY(30px)',
      down: 'translateY(-30px)',
      left: 'translateX(30px)',
      right: 'translateX(-30px)'
    };

    element.style.transform = transforms[direction];
    element.style.opacity = '0';
    element.style.transition = `all ${duration}ms ease-out`;
    
    setTimeout(() => {
      element.style.transform = 'none';
      element.style.opacity = '1';
    }, 10);
  }

  // Public API
  window.Animations = {
    init: initAnimations,
    stagger: staggerAnimation,
    animateNumber: animateNumber,
    typeWriter: typeWriter,
    fadeIn: fadeIn,
    slideIn: slideIn,
    isEnabled: () => animationsEnabled,
    isReducedMotion: () => reducedMotion
  };

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAnimations);
  } else {
    initAnimations();
  }

})();
