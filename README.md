# <PERSON><PERSON> Portfolio Website

A responsive, one-page portfolio website designed to showcase <PERSON><PERSON>'s work as a marketer, graphic designer, and UX designer.

## 🎨 Design Features

- **Color Palette**: Neutral, feminine, editorial aesthetic
  - BG-01: #F5F2EF (warm off-white)
  - BG-02: #EDE8E4 (taupe, default background)
  - Accent-1: #DCCFD8 (dusty mauve)
  - Accent-2: #B9A0C9 (muted lavender)
  - Text: #1A1A1A

- **Typography**: 
  - Headlines: Playfair Display Italic
  - Body: Inter

- **Aesthetic**: Cool, unique, neutral, feminine, editorial, clean

## 🏗️ Structure

1. **Hero Section** - Full-width with subtle parallax effect
2. **About Me** - Bio content with portrait image
3. **Work Gallery** - Tabbed interface with 4 categories:
   - Logos
   - TikTok/Reels
   - Flyers/Social Media
   - Photography
4. **Services & Rates** - Three service cards
5. **Testimonials** - Carousel with client quotes
6. **Contact** - Typeform integration + social links
7. **Footer** - Copyright and navigation

## 🚀 Technical Features

- **Responsive Design**: Mobile-first approach
- **Performance**: Lazy loading, LCP < 1.5s target
- **Accessibility**: WCAG AA compliance, reduced motion support
- **Modern Stack**: HTML5, SCSS (BEM), Vanilla JavaScript
- **Auto-Update Workflow**: Google Drive → Zapier → Airtable → Website

## 📁 Project Structure

```
web_port/
├── index.html              # Main HTML file
├── styles/
│   ├── main.scss           # Main SCSS entry
│   ├── main.css            # Compiled CSS
│   ├── _variables.scss     # Colors, typography, spacing
│   ├── _base.scss          # Reset, base styles
│   └── _components.scss    # BEM components
├── scripts/
│   ├── main.js             # Core functionality
│   ├── gallery.js          # Tabbed gallery
│   └── animations.js       # Micro-animations
├── assets/
│   ├── images/             # Portfolio images
│   └── icons/              # Social media icons
├── content/
│   ├── videos/             # Video content
│   ├── design/             # Design work
│   └── photo/              # Photography
└── package.json            # Dependencies
```

## 🛠️ Development Setup

1. **Clone/Download** the project
2. **Install dependencies**:
   ```bash
   npm install
   ```
3. **Compile SCSS** (if making style changes):
   ```bash
   npx sass styles/main.scss styles/main.css --watch
   ```
4. **Serve locally** using any static server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

## 🎯 CMS Integration (Future)

The website is designed to integrate with an auto-update workflow:

1. **Google Drive** - Content storage
2. **Zapier** - Automation trigger
3. **Airtable** - CMS database
4. **Website** - Auto-refresh content

### Airtable Schema (Planned)

**Gallery Table**:
- ID (Auto)
- Category (Single Select: Logos, Video, Social, Photo)
- Title (Text)
- Description (Long Text)
- Image URL (URL)
- Alt Text (Text)
- Order (Number)
- Published (Checkbox)

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## ♿ Accessibility Features

- Semantic HTML5 structure
- ARIA labels and roles
- Keyboard navigation support
- Focus management
- Color contrast compliance (WCAG AA)
- Reduced motion support
- Screen reader optimization

## 🎨 Design System

### Colors
```scss
--color-bg-01: #F5F2EF;     // Warm off-white
--color-bg-02: #EDE8E4;     // Taupe
--color-accent-01: #DCCFD8; // Dusty mauve
--color-accent-02: #B9A0C9; // Muted lavender
--color-text-primary: #1A1A1A;
```

### Typography Scale
```scss
--font-size-xs: 0.75rem;    // 12px
--font-size-sm: 0.875rem;   // 14px
--font-size-base: 1rem;     // 16px
--font-size-lg: 1.125rem;   // 18px
--font-size-xl: 1.25rem;    // 20px
--font-size-2xl: 1.5rem;    // 24px
--font-size-3xl: 1.875rem;  // 30px
--font-size-4xl: 2.25rem;   // 36px
--font-size-5xl: 3rem;      // 48px
--font-size-6xl: 3.75rem;   // 60px
```

### Spacing Scale
```scss
--space-1: 0.25rem;  // 4px
--space-2: 0.5rem;   // 8px
--space-4: 1rem;     // 16px
--space-8: 2rem;     // 32px
--space-12: 3rem;    // 48px
--space-16: 4rem;    // 64px
--space-24: 6rem;    // 96px
```

## 📋 TODO / Next Steps

- [ ] Add real content and images
- [ ] Set up Typeform integration
- [ ] Configure Airtable CMS
- [ ] Set up Zapier automation
- [ ] Deploy to hosting platform
- [ ] Set up custom domain
- [ ] Add Google Analytics
- [ ] Optimize images
- [ ] Add meta tags for SEO
- [ ] Test across devices

## 📄 License

This project is created for Daja Lucas. All rights reserved.

## 👥 Credits

- **Design & Development**: Augment Agent
- **Fonts**: Google Fonts (Playfair Display, Inter)
- **Icons**: Custom SVG icons
- **Framework**: Vanilla HTML/CSS/JS

---

**Contact**: For questions about this project, please reach out to Daja Lucas.
