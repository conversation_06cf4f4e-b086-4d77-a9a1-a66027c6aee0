// =============================================================================
// BASE STYLES - Reset, Typography & Global Styles
// =============================================================================

// CSS Reset & Normalize
// -----------------------------------------------------------------------------
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-02);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

// Typography
// -----------------------------------------------------------------------------
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

h1 {
  font-size: var(--font-size-4xl);
  font-style: italic;
  letter-spacing: var(--letter-spacing-tight);
  
  @include media-up('md') {
    font-size: var(--font-size-5xl);
  }
  
  @include media-up('lg') {
    font-size: var(--font-size-6xl);
  }
}

h2 {
  font-size: var(--font-size-3xl);
  font-style: italic;
  
  @include media-up('md') {
    font-size: var(--font-size-4xl);
  }
}

h3 {
  font-size: var(--font-size-2xl);
  
  @include media-up('md') {
    font-size: var(--font-size-3xl);
  }
}

h4 {
  font-size: var(--font-size-xl);
  
  @include media-up('md') {
    font-size: var(--font-size-2xl);
  }
}

h5 {
  font-size: var(--font-size-lg);
  
  @include media-up('md') {
    font-size: var(--font-size-xl);
  }
}

h6 {
  font-size: var(--font-size-base);
  
  @include media-up('md') {
    font-size: var(--font-size-lg);
  }
}

p {
  margin-bottom: var(--space-4);
  color: var(--color-text-secondary);
  
  &:last-child {
    margin-bottom: 0;
  }
}

// Links
// -----------------------------------------------------------------------------
a {
  color: var(--color-accent-02);
  text-decoration: none;
  transition: color var(--transition-fast);
  
  &:hover,
  &:focus {
    color: var(--color-text-primary);
    text-decoration: underline;
  }
  
  &:focus {
    outline: none;
    box-shadow: var(--focus-ring);
    border-radius: var(--radius-sm);
  }
}

// Lists
// -----------------------------------------------------------------------------
ul, ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

li {
  margin-bottom: var(--space-2);
  color: var(--color-text-secondary);
  
  &:last-child {
    margin-bottom: 0;
  }
}

// Images & Media
// -----------------------------------------------------------------------------
img {
  max-width: 100%;
  height: auto;
  display: block;
}

svg {
  display: block;
  max-width: 100%;
  height: auto;
}

// Forms
// -----------------------------------------------------------------------------
button,
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  
  &:focus {
    outline: none;
    box-shadow: var(--focus-ring);
    border-radius: var(--radius-sm);
  }
}

// Accessibility
// -----------------------------------------------------------------------------
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-text-primary);
  color: var(--color-white);
  padding: var(--space-2) var(--space-4);
  text-decoration: none;
  border-radius: var(--radius-base);
  z-index: var(--z-index-tooltip);
  
  &:focus {
    top: 6px;
  }
}

// Screen reader only content
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Focus visible for better keyboard navigation
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

// Container & Layout Utilities
// -----------------------------------------------------------------------------
.container {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  
  @include media-up('lg') {
    padding: 0 var(--container-padding-lg);
  }
}

.section {
  padding: var(--section-padding-y) 0;
  
  @include media-up('lg') {
    padding: var(--section-padding-y-lg) 0;
  }
}

// Grid System
// -----------------------------------------------------------------------------
.grid {
  display: grid;
  gap: var(--grid-gap);
  
  @include media-up('lg') {
    gap: var(--grid-gap-lg);
  }
}

.grid--2 {
  grid-template-columns: 1fr;
  
  @include media-up('md') {
    grid-template-columns: repeat(2, 1fr);
  }
}

.grid--3 {
  grid-template-columns: 1fr;
  
  @include media-up('md') {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include media-up('lg') {
    grid-template-columns: repeat(3, 1fr);
  }
}

.grid--4 {
  grid-template-columns: 1fr;
  
  @include media-up('sm') {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include media-up('lg') {
    grid-template-columns: repeat(4, 1fr);
  }
}

// Flexbox Utilities
// -----------------------------------------------------------------------------
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

// Text Utilities
// -----------------------------------------------------------------------------
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

// Spacing Utilities
// -----------------------------------------------------------------------------
.mb-0 { margin-bottom: 0; }
.mb-4 { margin-bottom: var(--space-4); }
.mb-8 { margin-bottom: var(--space-8); }
.mb-12 { margin-bottom: var(--space-12); }

.mt-0 { margin-top: 0; }
.mt-4 { margin-top: var(--space-4); }
.mt-8 { margin-top: var(--space-8); }
.mt-12 { margin-top: var(--space-12); }

// Display Utilities
// -----------------------------------------------------------------------------
.hidden {
  display: none;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

@include media-up('md') {
  .md\\:hidden {
    display: none;
  }
  
  .md\\:block {
    display: block;
  }
}

@include media-up('lg') {
  .lg\\:hidden {
    display: none;
  }
  
  .lg\\:block {
    display: block;
  }
}
