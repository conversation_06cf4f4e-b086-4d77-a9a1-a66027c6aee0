// =============================================================================
// VARIABLES - Color Palette, Typography & Spacing
// =============================================================================

// Color Palette
// -----------------------------------------------------------------------------
:root {
  // Background Colors
  --color-bg-01: #F5F2EF;        // Warm off-white (use sparingly)
  --color-bg-02: #EDE8E4;        // Taupe (default section background)
  
  // Accent Colors
  --color-accent-01: #DCCFD8;    // Dusty mauve
  --color-accent-02: #B9A0C9;    // Muted lavender
  
  // Text Colors
  --color-text-primary: #1A1A1A; // Primary text
  --color-text-secondary: #4A4A4A; // Secondary text
  --color-text-light: #6B6B6B;   // Light text
  --color-text-white: #FFFFFF;   // White text
  
  // Utility Colors
  --color-white: #FFFFFF;
  --color-black: #000000;
  --color-transparent: transparent;
  
  // Gradients
  --gradient-primary: linear-gradient(135deg, var(--color-accent-01) 0%, var(--color-accent-02) 100%);
  --gradient-overlay: linear-gradient(180deg, rgba(26, 26, 26, 0.6) 0%, rgba(26, 26, 26, 0.3) 100%);
}

// Typography
// -----------------------------------------------------------------------------
:root {
  // Font Families
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-heading: 'Playfair Display', Georgia, serif;
  
  // Font Weights
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  // Font Sizes - Mobile First
  --font-size-xs: 0.75rem;      // 12px
  --font-size-sm: 0.875rem;     // 14px
  --font-size-base: 1rem;       // 16px
  --font-size-lg: 1.125rem;     // 18px
  --font-size-xl: 1.25rem;      // 20px
  --font-size-2xl: 1.5rem;      // 24px
  --font-size-3xl: 1.875rem;    // 30px
  --font-size-4xl: 2.25rem;     // 36px
  --font-size-5xl: 3rem;        // 48px
  --font-size-6xl: 3.75rem;     // 60px
  
  // Line Heights
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  // Letter Spacing
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
}

// Spacing Scale
// -----------------------------------------------------------------------------
:root {
  --space-1: 0.25rem;    // 4px
  --space-2: 0.5rem;     // 8px
  --space-3: 0.75rem;    // 12px
  --space-4: 1rem;       // 16px
  --space-5: 1.25rem;    // 20px
  --space-6: 1.5rem;     // 24px
  --space-8: 2rem;       // 32px
  --space-10: 2.5rem;    // 40px
  --space-12: 3rem;      // 48px
  --space-16: 4rem;      // 64px
  --space-20: 5rem;      // 80px
  --space-24: 6rem;      // 96px
  --space-32: 8rem;      // 128px
  --space-40: 10rem;     // 160px
  --space-48: 12rem;     // 192px
  --space-56: 14rem;     // 224px
  --space-64: 16rem;     // 256px
}

// Container & Layout
// -----------------------------------------------------------------------------
:root {
  --container-max-width: 1200px;
  --container-padding: var(--space-4);
  --container-padding-lg: var(--space-6);
  
  --section-padding-y: var(--space-16);
  --section-padding-y-lg: var(--space-24);
  
  --grid-gap: var(--space-6);
  --grid-gap-lg: var(--space-8);
}

// Border Radius
// -----------------------------------------------------------------------------
:root {
  --radius-sm: 0.125rem;   // 2px
  --radius-base: 0.25rem;  // 4px
  --radius-md: 0.375rem;   // 6px
  --radius-lg: 0.5rem;     // 8px
  --radius-xl: 0.75rem;    // 12px
  --radius-2xl: 1rem;      // 16px
  --radius-3xl: 1.5rem;    // 24px
  --radius-full: 9999px;   // Full rounded
}

// Shadows
// -----------------------------------------------------------------------------
:root {
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}

// Transitions & Animations
// -----------------------------------------------------------------------------
:root {
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  --ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out-cubic: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-cubic: cubic-bezier(0.4, 0, 1, 1);
}

// Z-Index Scale
// -----------------------------------------------------------------------------
:root {
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

// Breakpoints (for reference in media queries)
// -----------------------------------------------------------------------------
$breakpoints: (
  'xs': 0,
  'sm': 640px,
  'md': 768px,
  'lg': 1024px,
  'xl': 1280px,
  '2xl': 1536px
);

// Media Query Mixins
// -----------------------------------------------------------------------------
@mixin media-up($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $value: map-get($breakpoints, $breakpoint);
    @if $value > 0 {
      @media (min-width: $value) {
        @content;
      }
    } @else {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin media-down($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $value: map-get($breakpoints, $breakpoint);
    @if $value > 0 {
      @media (max-width: $value - 1px) {
        @content;
      }
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin media-between($lower, $upper) {
  @if map-has-key($breakpoints, $lower) and map-has-key($breakpoints, $upper) {
    $lower-value: map-get($breakpoints, $lower);
    $upper-value: map-get($breakpoints, $upper);
    
    @media (min-width: $lower-value) and (max-width: $upper-value - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$lower} or #{$upper}";
  }
}

// Accessibility
// -----------------------------------------------------------------------------
:root {
  --focus-ring: 0 0 0 3px rgba(185, 160, 201, 0.5); // Using accent-02 with opacity
  --focus-ring-offset: 2px;
}

// Animation preferences
// -----------------------------------------------------------------------------
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0ms;
    --transition-base: 0ms;
    --transition-slow: 0ms;
  }
  
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
