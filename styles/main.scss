// =============================================================================
// MAIN SCSS - <PERSON><PERSON>
// =============================================================================

// Import all partials in the correct order
@import 'variables';
@import 'base';
@import 'components';

// Additional utility classes and overrides can go here
// =============================================================================

// Smooth scrolling for anchor links
html {
  scroll-padding-top: 80px; // Account for fixed header
}

// Loading states
.loading {
  opacity: 0.5;
  pointer-events: none;
  transition: opacity var(--transition-base);
}

// Fade in animation for sections
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--transition-slow);
  
  &.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

// Image lazy loading placeholder
.lazy-image {
  background: var(--color-bg-01);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    animation: shimmer 1.5s infinite;
  }
  
  img {
    transition: opacity var(--transition-base);
    
    &.loaded {
      opacity: 1;
    }
    
    &:not(.loaded) {
      opacity: 0;
    }
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// Print styles
@media print {
  .header,
  .footer,
  .btn,
  .nav__toggle {
    display: none !important;
  }
  
  .hero {
    min-height: auto;
    page-break-after: always;
  }
  
  .section {
    page-break-inside: avoid;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
    color: #000;
    background: #fff;
  }
  
  h1, h2, h3 {
    page-break-after: avoid;
  }
}
